package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;
import com.paic.ncbs.claim.common.constant.ReportConstant;

public enum ReportSubModeEnum {

    INTERNAL_SUB_01(ReportConstant.REPORSUBTMODE_INTERNAL_01, "柜面"),
    OFFICIAL_ACCOUNTS_01(ReportConstant.REPORTSUBMODE_OFFICIAL_ACCOUNTS_01, "公众号报案"),
    CUSTOMER_SERVICE_SYSTEM_01(ReportConstant.REPORTSUBMODE_CUSTOMER_SERVICE_SYSTEM_01, "客服报案"),
    MINI_PROGRAM(ReportConstant.REPORTSUBMODE_SMALL_PROGRAM_01, "小程序报案"),
    FREIGHT_INSURANCE(ReportConstant.REPORTSUBMODE_ONLINE_01, "退运费险报案"),
    WESURE(ReportConstant.REPORTSUBMODE_ONLINE_02, "微保报案"),
    XIEZHU(ReportConstant.REPORTSUBMODE_ONLINE_03, "谐筑报案"),
    MSH(ReportConstant.REPORTSUBMODE_ONLINE_04, "万欣和报案"),
    FENGSHI(ReportConstant.REPORTSUBMODE_ONLINE_05, "风石报案"),
    MEDI_LINK(ReportConstant.REPORTSUBMODE_ONLINE_06, "中间带报案"),
    TENCENT_HEALTH_CARE(ReportConstant.REPORTSUBMODE_ONLINE_07, "腾讯健康报案"),
    OTHER_CHANNEL_REPORT(ReportConstant.REPORTSUBMODE_ONLINE_08, "外部通过渠道报案"),
    DAOTIAN(ReportConstant.REPORTSUBMODE_ONLINE_09, "稻甜报案"),
    JIKE(ReportConstant.REPORTSUBMODE_ONLINE_10, "极客报案"),
    DIANPING_ONLINE(ReportConstant.REPORTMODE_DIANPING_ONLINE_01, "大众点评报案");

    private final String type;
    private final String name;

    ReportSubModeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getName(String type) {
        if(StrUtil.isEmpty(type)){
            return null;
        }
        for (ReportSubModeEnum value : ReportSubModeEnum.values()) {
            if (type.equals(value.getType())) {
                return value.getName();
            }
        }
        return null;
    }
}
