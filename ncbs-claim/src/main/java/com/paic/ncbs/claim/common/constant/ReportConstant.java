package com.paic.ncbs.claim.common.constant;

import java.util.HashMap;
import java.util.Map;

public class ReportConstant {

    public static final String REPORT_MODE_COUNTER = "2";

    public static final String REPORT_MODE_PTS = "5";

    public static final String REPORT_MODE_TONG_CHENG = "LY";

    public static final String INSURED_APPLY_STATUS_MEDICAL_TREATMENT = "IS_0101";
    public static final String INSURED_APPLY_STATUS_CRITICAL_DISEASES = "IS_0102";
    public static final String INSURED_APPLY_STATUS_PERMANENT_DISABILITY = "IS_0103";
    public static final String INSURED_APPLY_STATUS_DIED = "IS_0104";
    public static final String INSURED_APPLY_STATUS_DISAPPEAR = "IS_0105";
    public static final String INSURED_APPLY_STATUS_FLIGHT = "IS_0108";
    public static final String INSURED_APPLY_STATUS_PET = "IS_0119";

    public static final String INIT_CASE_TIMES = BaseConstant.STRING_1;

    public static final String INIT_CASE_STATUS = BaseConstant.STRING_1;

    /**
     * 正常报案:1,异常报案:2
     */
    public static final String NORMAL = BaseConstant.STRING_1;

    public static final String ABNORMAL = "2";

    public static final String HURT = BaseConstant.STRING_1;

    public static final String NOT_HURT = "2";


    public static final String REPROTED = BaseConstant.STRING_1;

    public static final Map<String, String> ACCIDENT_TYPE = new HashMap<>();
    public static final String REPORTMODE_MINI_PROGRAM = "6";

    static {
        ACCIDENT_TYPE.put("1", "单方");
        ACCIDENT_TYPE.put("2", "多方");
    }

    public static final Map<String, String> WHOLE_CASE_STATUS = new HashMap<>();

    static {
        WHOLE_CASE_STATUS.put("0", "已结案");
        WHOLE_CASE_STATUS.put("1", "已报案");
        WHOLE_CASE_STATUS.put("2", "已理算");

    }

    public static final String AHCS_BATCH_REPORT_MERGE_Y = "Y";
    public static final String AHCS_BATCH_REPORT_MERGE_N = "N";

    public final static String APPLICANT_TYPE_ONE = "01";
    public final static String APPLICANT_TYPE_TWO = "02";
    public final static String APPLICANT_TYPE_THREE = "03";

    public static final String REPORTMODE_CALL = "1";

    /**
     * 线下
     */
    public static final String REPORTMODE_INTERNAL = "2";
    public static final String REPORSUBTMODE_INTERNAL_01 = "2-01";

    // 小程序报案
    public static final String REPORTMODE_SMALL_PROGRAM = "6";
    public static final String REPORTSUBMODE_SMALL_PROGRAM_01 = "6-01";

    // 客服系统报案
    public static final String CUSTOMER_SERVICE_SYSTEM = "7";
    public static final String REPORTSUBMODE_CUSTOMER_SERVICE_SYSTEM_01 = "7-01";

    // 公众号报案
    public static final String REPORTMODE_OFFICIAL_ACCOUNTS = "8";
    public static final String REPORTSUBMODE_OFFICIAL_ACCOUNTS_01 = "8-01";

    // 线上微保报案
    public static final String REPORTMODE_ONLINE = "9";
    public static final String REPORTSUBMODE_ONLINE_01 = "9-01";
    public static final String REPORTSUBMODE_ONLINE_02 = "9-02";
    public static final String REPORTSUBMODE_ONLINE_03 = "9-03";
    public static final String REPORTSUBMODE_ONLINE_04 = "9-04";
    public static final String REPORTSUBMODE_ONLINE_05 = "9-05";
    public static final String REPORTSUBMODE_ONLINE_06 = "9-06";
    public static final String REPORTSUBMODE_ONLINE_07 = "9-07";
    public static final String REPORTSUBMODE_ONLINE_08 = "9-08";
    public static final String REPORTSUBMODE_ONLINE_09 = "9-09";
    public static final String REPORTSUBMODE_ONLINE_10 = "9-10";

    // 线上大众点评报案
    public static final String REPORTMODE_DIANPING_ONLINE = "10";
    public static final String REPORTMODE_DIANPING_ONLINE_01 = "10-01";

    // 顺丰这类的快递公司
    public static final String PAYMENTMODE_COMPANY_PAY = "COMPANY_PAY";

    // 微保线上的
    public static final String PAYMENTMODE_ONLINE_PAY = "ONLINE_PAY";

    public static final String CASE_TYPE_FAST = "02";

    //1-线上退运险、2-大众点评责任险
    public static final String THREE_SOURCE_TUIYUNXIAN_NAME = "线上退运险";

    //1-线上退运险、2-大众点评责任险
    public static final String THREE_SOURCE_DIANPING = "2";
    public static final String THREE_SOURCE_DIANPING_NAME = "大众点评责任险";



}
