package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.ReportConstant;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "线上报案信息")
public class OnlineReportVO {

    private String reportAcceptUm = ConstValues.SYSTEM_UM;

    /**
     * 出险者现状 对应枚举 InsuredApplyStatusEnum
     */
    @ApiModelProperty(value = "出险者现状")
    private String insuredApplyStatus;

    @ApiModelProperty(value = "损伤外部原因代码")
    private String injuryReasonCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "出险时间")
    private Date accidentDate;

    /**
     * 事故类型 对应枚举 AccidentTypeEnum
     */
    @ApiModelProperty(value = "事故类型（疾病、意外）")
    private String accidentType;

    @ApiModelProperty(value = "是否大灾（Y-是 N-否）")
    private String isHugeAccident;

    /**
     * 出险类型 对应枚举 InsuredApplyTypeEnum
     */
    @ApiModelProperty(value = "出险类型")
    private String insuredApplyType;

    @ApiModelProperty(value = "出险原因大类")
    private String accidentCauseLevel1;

    @ApiModelProperty(value = "出险原因明细类")
    private String accidentCauseLevel2;

    @ApiModelProperty(value = "主要描述 描述事故经过 存在报案查勘说明里")
    private String accidentDetail;

    @ApiModelProperty(value = "事故地点(0：境内/1：境外)")
    private String whetherOutSideAccident;

    @ApiModelProperty(value = "出险省份")
    private String accidentProvince;

    @ApiModelProperty(value = "出险城市")
    private String accidentCity;

    @ApiModelProperty(value = "出险县")
    private String accidentCounty;

    @ApiModelProperty(value = "出险区域")
    private String accidentArea;

    @ApiModelProperty(value = "出险国家")
    private String accidentNation;

    @ApiModelProperty(value = "出险区域")
    private String accidentPlace;

    @ApiModelProperty(value = "保单号列表")
    private List<String> policyNos;

    @ApiModelProperty(value = "是否特殊报案")
    private String isSpecialReport = "N";

    @ApiModelProperty(value = "客户名")
    private String clientName;

    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    @ApiModelProperty(value = "身份证号")
    private String certificateNo;

    @ApiModelProperty(value = "损失类别 6-其他")
    private String caseClass = "6";

    @ApiModelProperty(value = "是否单证齐全（Y/N）")
    private String isSuffice;

    @ApiModelProperty(value = "报案说明")
    private String remark;

    @ApiModelProperty(value = "医疗状态:MS_2201 未治疗;MS_2202 治疗中;MS_2203 治疗结束;MS_2204 不详")
    private String medicalStatus;

    @ApiModelProperty(value = "就诊类型：01-门急诊，02-住院医疗")
    private String medicalType;

    @ApiModelProperty(value = "就诊医院编码 MS_2202 治疗中;MS_2203 治疗结束 必填")
    private String hospitalCode;

    @ApiModelProperty(value = "就诊医院 MS_2202 治疗中;MS_2203 治疗结束 必填")
    private String hospitalName;

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ApiModelProperty(value = "伤害原因 公众号在线理赔+")
    private String injuryMechanism;

    @ApiModelProperty(value = "伤害原因描述 公众号在线理赔+")
    private String injuryMechanismDesc;

    @ApiModelProperty(value = "就诊原因名称 公众号在线理赔+")
    private String visitReason;

    @ApiModelProperty(value = "职业大类类型（微保接口两个职业的区别）")
    private String professionCode;

    @ApiModelProperty(value = "职业小类类型")
    private String subProfessionCode;

    @ApiModelProperty(value = "居住地址")
    private String residenceAddress;

    @ApiModelProperty(value = "居住省")
    private String residenceProvince;

    @ApiModelProperty(value = "居住市")
    private String residenceCity;

    @ApiModelProperty(value = "居住区")
    private String residenceDistrict;

    @ApiModelProperty(value = "申请金额")
    private String applyAmount;

    @ApiModelProperty(value = "案件类别")
    private List<String> reportTypeList;

    @ApiModelProperty(value = "联系人信息")
    private List<LinkManEntity> linkManList = new ArrayList<>();

    @ApiModelProperty(value = "领款人信息")
    private List<PaymentInfoVO> paymentList = new ArrayList<>();

    /**
     *报案来源(参考clm_common_parameter.collection_code = REPORT_FROM_TYPE)
     */
    @ApiModelProperty(value = "报案来源")
    private String reportMode = ReportConstant.REPORTMODE_ONLINE;

    @ApiModelProperty(value = "报案来源小类")
    private String reportSubMode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "报案时间")
    private Date reportDate = new Date();

    @ApiModelProperty(value = "自动理算标记 Y-微保进行自动理算 N-微保不进行自动理算")
    private String wesureAutoClaim;
    @ApiModelProperty(value = "风险等级得分 1-5分 分数越高风险越高")
    private Integer riskLevelScore;
    @ApiModelProperty(value = "风险说明 示例:用户有既往症风险;用户涉及密集投保;用户有多次理赔记录")
    private String riskLevelDesc;
    @ApiModelProperty(value = "TPA公司ID")
    private String companyId;

    @ApiModelProperty(value = "微保端额外信息")
    private String isQuickPay;

    @ApiModelProperty(value = "理赔处理方式")
    private String claimDealWay;

    @ApiModelProperty(value = "渠道报案受理单号")
    private String acceptanceNumber;

    @ApiModelProperty(value = "是否死亡 （Y-是  N-否）")
    private	String deathYN;

    @ApiModelProperty(value = "是否急救（Y-是  N-否）")
    private	String firstAidYN;

    @ApiModelProperty(value = "险种标志 GZ-雇主 TY-团意 OTHER-其他")
    private	String riskFlag;

    @ApiModelProperty(value = "是否骨折（Y-是  N-否）")
    private	String fractureYN;

    @ApiModelProperty(value = "是否高坠（Y-是  N-否）")
    private	String highFallYN;

    @ApiModelProperty(value = "TPA处理人代码")
    private String tpaCom;

    @ApiModelProperty(value = "TPA处理人名称")
    private String tpaComName;

    @ApiModelProperty(value = "标的")
    private List<CaseRiskPropertyDTO> riskPropertyList;

}
